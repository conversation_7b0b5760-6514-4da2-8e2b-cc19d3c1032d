import svgPaths from "@/components/imports/svg-9r6m2ztw2h";
import { useState } from "react";

export function Lines() {
  const [hoveredRect, setHoveredRect] = useState<string | null>(null);

  return (
    <div className="w-full max-w-[440px] h-[300px] sm:h-[350px] lg:h-[441px] mx-auto lg:mx-0 animate-fade-in-up animation-delay-800">
      <div className="w-full h-full animate-float">
        <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 440 442">
          <defs>
            {/* Neon glow filter */}
            <filter id="neonGlow" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur stdDeviation="4" result="coloredBlur" />
              <feMerge>
                <feMergeNode in="coloredBlur" />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>

            {/* Stronger outer glow */}
            <filter id="strongGlow" x="-100%" y="-100%" width="300%" height="300%">
              <feGaussianBlur stdDeviation="8" result="coloredBlur" />
              <feMerge>
                <feMergeNode in="coloredBlur" />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>

            {/* Interactive glow filter */}
            <filter id="hoverGlow" x="-100%" y="-100%" width="300%" height="300%">
              <feGaussianBlur stdDeviation="6" result="coloredBlur" />
              <feColorMatrix values="0 1 1 0 0  0 1 1 0 0  0 1 1 0 0  0 0 0 1 0" result="cyan" />
              <feMerge>
                <feMergeNode in="cyan" />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>

            {/* Animation path that follows the grid circuit */}
            <path id="circuitPath" d="M 4 4 L 439 4 L 439 86 L 4 86 L 4 168 L 439 168 L 439 250 L 4 250 L 4 332 L 439 332 L 439 440 L 254 440 L 254 5 L 170 5 L 170 440 L 87 440 L 87 5 L 4 5 L 4 4"
              fill="none" stroke="none" />
          </defs>

          <g id="Group 1">
            <line id="Line 2" stroke="var(--stroke-0, #5D5959)" x1="3.92771" x2="439.146" y1="4.24722" y2="4.24722" />
            <path d={svgPaths.p50280} id="Line 7" stroke="var(--stroke-0, #5D5959)" />
            <line id="Line 3" stroke="var(--stroke-0, #5D5959)" x1="3.92771" x2="439.146" y1="86.1975" y2="86.1975" />
            <line id="Line 8" stroke="var(--stroke-0, #5D5959)" x1="254.196" x2="254.196" y1="440.345" y2="5.12674" />
            <line id="Line 4" stroke="var(--stroke-0, #5D5959)" x1="3.92771" x2="439.146" y1="168.148" y2="168.148" />
            <line id="Line 9" stroke="var(--stroke-0, #5D5959)" x1="170.606" x2="170.606" y1="440.725" y2="5.50638" />
            <line id="Line 5" stroke="var(--stroke-0, #5D5959)" x1="3.92771" x2="439.146" y1="250.098" y2="250.098" />
            <line id="Line 10" stroke="var(--stroke-0, #5D5959)" x1="87.017" x2="87.017" y1="441.104" y2="5.88596" />
            <line id="Line 6" stroke="var(--stroke-0, #5D5959)" x1="3.92771" x2="439.146" y1="332.048" y2="332.048" />
            <path d={svgPaths.p1e0edc00} id="Line 12" stroke="var(--stroke-0, #5D5959)" />
            <line id="Line 11" stroke="var(--stroke-0, #5D5959)" x1="4.73694" x2="4.73694" y1="439.903" y2="5.23697" />

            {/* Interactive rectangles */}
            <rect
              fill={hoveredRect === "rect8" ? "#00ffff" : "var(--fill-0, #D9D9D9)"}
              height="9.16465"
              id="Rectangle 8"
              width="9.16465"
              className="cursor-pointer transition-all duration-300"
              filter={hoveredRect === "rect8" ? "url(#hoverGlow)" : ""}
              onMouseEnter={() => setHoveredRect("rect8")}
              onMouseLeave={() => setHoveredRect(null)}
            />
            <rect
              fill={hoveredRect === "rect9" ? "#00ffff" : "var(--fill-0, #D9D9D9)"}
              height="9.16465"
              id="Rectangle 9"
              width="9.16465"
              y="79.8634"
              className="cursor-pointer transition-all duration-300"
              filter={hoveredRect === "rect9" ? "url(#hoverGlow)" : ""}
              onMouseEnter={() => setHoveredRect("rect9")}
              onMouseLeave={() => setHoveredRect(null)}
            />
            <rect
              fill={hoveredRect === "rect10" ? "#00ffff" : "var(--fill-0, #D9D9D9)"}
              height="9.16465"
              id="Rectangle 10"
              width="9.16465"
              x="82.4818"
              y="81.1726"
              className="cursor-pointer transition-all duration-300"
              filter={hoveredRect === "rect10" ? "url(#hoverGlow)" : ""}
              onMouseEnter={() => setHoveredRect("rect10")}
              onMouseLeave={() => setHoveredRect(null)}
            />
            <rect
              fill={hoveredRect === "rect12" ? "#00ffff" : "var(--fill-0, #D9D9D9)"}
              height="9.16465"
              id="Rectangle 12"
              width="9.16465"
              x="332.546"
              y="327.309"
              className="cursor-pointer transition-all duration-300"
              filter={hoveredRect === "rect12" ? "url(#hoverGlow)" : ""}
              onMouseEnter={() => setHoveredRect("rect12")}
              onMouseLeave={() => setHoveredRect(null)}
            />
            <rect
              fill={hoveredRect === "rect11" ? "#00ffff" : "var(--fill-0, #D9D9D9)"}
              height="9.16465"
              id="Rectangle 11"
              width="9.16465"
              x="82.4818"
              className="cursor-pointer transition-all duration-300"
              filter={hoveredRect === "rect11" ? "url(#hoverGlow)" : ""}
              onMouseEnter={() => setHoveredRect("rect11")}
              onMouseLeave={() => setHoveredRect(null)}
            />

            {/* Neon light beam animation */}
            <g>
              {/* Wide outer glow - cyan/blue */}
              <circle r="12" fill="#0080ff" filter="url(#strongGlow)" opacity="0.3">
                <animateMotion dur="15s" repeatCount="indefinite">
                  <mpath href="#circuitPath" />
                </animateMotion>
              </circle>

              {/* Medium glow - electric blue */}
              <circle r="6" fill="#00bfff" filter="url(#neonGlow)" opacity="0.7">
                <animateMotion dur="15s" repeatCount="indefinite">
                  <mpath href="#circuitPath" />
                </animateMotion>
              </circle>

              {/* Main beam - bright cyan */}
              <circle r="3" fill="#00ffff" filter="url(#neonGlow)">
                <animateMotion dur="15s" repeatCount="indefinite">
                  <mpath href="#circuitPath" />
                </animateMotion>
              </circle>

              {/* Core bright point - white */}
              <circle r="1.5" fill="#ffffff">
                <animateMotion dur="15s" repeatCount="indefinite">
                  <mpath href="#circuitPath" />
                </animateMotion>
              </circle>

              {/* Trail effects with delays */}
              <circle r="4" fill="#00bfff" opacity="0.5">
                <animateMotion dur="15s" repeatCount="indefinite" begin="0.3s">
                  <mpath href="#circuitPath" />
                </animateMotion>
              </circle>

              <circle r="3" fill="#0080ff" opacity="0.3">
                <animateMotion dur="15s" repeatCount="indefinite" begin="0.6s">
                  <mpath href="#circuitPath" />
                </animateMotion>
              </circle>

              <circle r="2" fill="#0060df" opacity="0.2">
                <animateMotion dur="15s" repeatCount="indefinite" begin="0.9s">
                  <mpath href="#circuitPath" />
                </animateMotion>
              </circle>
            </g>
          </g>
        </svg>
      </div>
    </div>
  );
}