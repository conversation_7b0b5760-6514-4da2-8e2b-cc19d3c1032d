'use client'

import { Star } from "lucide-react";
import { useState } from "react";

export function Footer() {
  const [email, setEmail] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isSubscribing, setIsSubscribing] = useState(false);

  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;
    
    setIsSubscribing(true);
    // Simulate subscription
    setTimeout(() => {
      setIsSubscribing(false);
      setIsSubscribed(true);
      setEmail("");
      // Reset after 3 seconds
      setTimeout(() => setIsSubscribed(false), 3000);
    }, 1500);
  };

  return (
    <footer className="bg-[#131313] border-t border-[#5D5959] animate-fade-in-up">
      <div className="max-w-[1440px] mx-auto px-4 sm:px-8 lg:px-[98px] py-12 lg:py-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12 mb-8 lg:mb-12">
          {/* Brand Section */}
          <div className="col-span-1 md:col-span-1 animate-fade-in-up animation-delay-200">
            <div className="flex items-center gap-3 mb-4 group cursor-pointer">
              <div className="relative">
                <div className="h-[40px] w-[40px] flex items-center justify-center">
                  <Star className="h-6 w-6 text-[#D9D9D9] fill-[#D9D9D9] rotate-[29.505deg] group-hover:text-[#00ffff] group-hover:fill-[#00ffff] group-hover:rotate-[389.505deg] transition-all duration-500" />
                </div>
              </div>
              <h3 className="font-['Splash:Regular',_sans-serif] text-[24px] text-[#ffffff] group-hover:text-[#00ffff] transition-colors duration-300">Motion</h3>
            </div>
            <p className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 leading-relaxed max-w-[280px] hover:opacity-100 transition-opacity duration-300">
              Transforming equations into visual experiences with the power of generative AI. No limits to your creativity.
            </p>
          </div>

          {/* Product Section */}
          <div className="col-span-1 animate-fade-in-up animation-delay-400">
            <h4 className="font-['Poppins:SemiBold',_sans-serif] text-[16px] text-[#ffffff] mb-6">Product</h4>
            <ul className="space-y-4">
              <li><a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:translate-x-1 transition-all duration-200">Features</a></li>
              <li><a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:translate-x-1 transition-all duration-200">Pricing</a></li>
              <li><a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:translate-x-1 transition-all duration-200">API</a></li>
              <li><a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:translate-x-1 transition-all duration-200">Documentation</a></li>
            </ul>
          </div>

          {/* Company Section */}
          <div className="col-span-1 animate-fade-in-up animation-delay-600">
            <h4 className="font-['Poppins:SemiBold',_sans-serif] text-[16px] text-[#ffffff] mb-6">Company</h4>
            <ul className="space-y-4">
              <li><a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:translate-x-1 transition-all duration-200">About</a></li>
              <li><a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:translate-x-1 transition-all duration-200">Blog</a></li>
              <li><a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:translate-x-1 transition-all duration-200">Careers</a></li>
              <li><a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:translate-x-1 transition-all duration-200">Contact</a></li>
            </ul>
          </div>

          {/* Resources Section */}
          <div className="col-span-1 animate-fade-in-up animation-delay-800">
            <h4 className="font-['Poppins:SemiBold',_sans-serif] text-[16px] text-[#ffffff] mb-6">Resources</h4>
            <ul className="space-y-4">
              <li><a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:translate-x-1 transition-all duration-200">Help Center</a></li>
              <li><a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:translate-x-1 transition-all duration-200">Community</a></li>
              <li><a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:translate-x-1 transition-all duration-200">Privacy Policy</a></li>
              <li><a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:translate-x-1 transition-all duration-200">Terms of Service</a></li>
            </ul>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-[#5D5959] my-8 animate-fade-in-up animation-delay-1000"></div>

        {/* Bottom Footer */}
        <div className="flex flex-col md:flex-row justify-between items-center gap-6 animate-fade-in-up animation-delay-1200">
          <div className="flex items-center gap-6">
            <p className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-60">
              © 2025 Motion. All rights reserved.
            </p>
          </div>

          {/* Social Links */}
          <div className="flex items-center gap-6">
            <a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:scale-110 transition-all duration-200">
              Twitter
            </a>
            <a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:scale-110 transition-all duration-200">
              LinkedIn
            </a>
            <a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:scale-110 transition-all duration-200">
              GitHub
            </a>
            <a href="#" className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 hover:opacity-100 hover:text-[#00ffff] hover:scale-110 transition-all duration-200">
              Discord
            </a>
          </div>
        </div>

        {/* Newsletter Section */}
        <div className="mt-12 pt-8 border-t border-[#5D5959] animate-fade-in-up animation-delay-1400">
          <div className="max-w-md">
            <h4 className="font-['Poppins:SemiBold',_sans-serif] text-[16px] text-[#ffffff] mb-3">
              Stay updated
            </h4>
            <p className="font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] opacity-70 mb-4">
              Get the latest updates on new features and improvements.
            </p>
            <form onSubmit={handleSubscribe} className="flex flex-col sm:flex-row gap-3">
              <input 
                type="email" 
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                disabled={isSubscribing || isSubscribed}
                className="flex-1 bg-[#343434] border border-[#5D5959] rounded-[8px] px-4 py-3 font-['Poppins:Regular',_sans-serif] text-[14px] text-[#ffffff] placeholder:text-[#ffffff] placeholder:opacity-50 focus:outline-none focus:border-[#00ffff] focus:shadow-lg focus:shadow-[#00ffff]/20 transition-all duration-200 disabled:opacity-50"
              />
              <button 
                type="submit"
                disabled={isSubscribing || isSubscribed || !email}
                className="bg-[#343434] hover:bg-[#4a4a4a] border border-[#5D5959] hover:border-[#00ffff] rounded-[8px] px-6 py-3 font-['Poppins:Medium',_sans-serif] text-[14px] text-[#ffffff] transition-all duration-200 whitespace-nowrap disabled:opacity-50 hover:shadow-lg hover:shadow-[#00ffff]/20 hover:scale-105 relative overflow-hidden"
              >
                {isSubscribing ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Subscribing...
                  </div>
                ) : isSubscribed ? (
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Subscribed!
                  </div>
                ) : (
                  'Subscribe'
                )}
              </button>
            </form>
          </div>
        </div>
      </div>
    </footer>
  );
}