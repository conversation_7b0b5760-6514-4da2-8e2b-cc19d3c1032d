import svgPaths from "../imports/svg-9r6m2ztw2h";
import { useState, useRef } from "react";

function HeroText() {
  const [isLoading, setIsLoading] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleMagneticEffect = (e: React.MouseEvent<HTMLButtonElement>) => {
    const button = buttonRef.current;
    if (!button) return;

    const rect = button.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    const moveX = (x - centerX) * 0.15;
    const moveY = (y - centerY) * 0.15;

    button.style.transform = `translate(${moveX}px, ${moveY}px) scale(1.02)`;
  };

  const handleMouseLeave = () => {
    const button = buttonRef.current;
    if (!button) return;
    button.style.transform = 'translate(0px, 0px) scale(1)';
  };

  const handleClick = () => {
    setIsLoading(true);
    // Create ripple effect
    const button = buttonRef.current;
    if (button) {
      const ripple = document.createElement('span');
      ripple.className = 'absolute inset-0 rounded-[65px] bg-white opacity-20 animate-ping';
      button.appendChild(ripple);
      setTimeout(() => ripple.remove(), 600);
    }
    // Simulate loading
    setTimeout(() => setIsLoading(false), 2000);
  };

  return (
    <div className="flex flex-col justify-center min-h-[500px] lg:min-h-[600px] px-4 lg:px-0 animate-fade-in-up">
      <div className="max-w-[627px]">
        <h1 className="font-['Poppins:SemiBold',_sans-serif] text-[28px] sm:text-[35px] lg:text-[40px] text-[#ffffff] leading-[1.2] mb-8 lg:mb-12 animate-fade-in-up animation-delay-200">
          Transforming you're equation a visual edge
        </h1>
        <p className="font-['Poppins:Regular',_sans-serif] text-[16px] sm:text-[18px] lg:text-[20px] text-[#ffffff] leading-[1.4] mb-8 lg:mb-12 max-w-[425px] animate-fade-in-up animation-delay-400">
          Make every equation to a life that visualize better using generative AI
        </p>
        <button 
          ref={buttonRef}
          onMouseMove={handleMagneticEffect}
          onMouseLeave={handleMouseLeave}
          onClick={handleClick}
          disabled={isLoading}
          className="relative bg-[#343434] hover:bg-[#4a4a4a] transition-all duration-200 h-[42px] px-6 rounded-[65px] font-['Inter:Semi_Bold',_sans-serif] font-semibold text-[15px] text-[#ffffff] hover:shadow-lg hover:shadow-[#00ffff]/20 animate-fade-in-up animation-delay-600 disabled:opacity-70 overflow-hidden"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Loading...
            </div>
          ) : (
            'Get started'
          )}
        </button>
      </div>
    </div>
  );
}

function Group1() {
  const [hoveredRect, setHoveredRect] = useState<string | null>(null);

  return (
    <div className="w-full max-w-[440px] h-[300px] sm:h-[350px] lg:h-[441px] mx-auto lg:mx-0 animate-fade-in-up animation-delay-800">
      <div className="w-full h-full animate-float">
        <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 440 442">
          <defs>
            {/* Neon glow filter */}
            <filter id="neonGlow" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
              <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
            
            {/* Stronger outer glow */}
            <filter id="strongGlow" x="-100%" y="-100%" width="300%" height="300%">
              <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
              <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>

            {/* Interactive glow filter */}
            <filter id="hoverGlow" x="-100%" y="-100%" width="300%" height="300%">
              <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
              <feColorMatrix values="0 1 1 0 0  0 1 1 0 0  0 1 1 0 0  0 0 0 1 0" result="cyan"/>
              <feMerge> 
                <feMergeNode in="cyan"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
            
            {/* Animation path that follows the grid circuit */}
            <path id="circuitPath" d="M 4 4 L 439 4 L 439 86 L 4 86 L 4 168 L 439 168 L 439 250 L 4 250 L 4 332 L 439 332 L 439 440 L 254 440 L 254 5 L 170 5 L 170 440 L 87 440 L 87 5 L 4 5 L 4 4" 
                  fill="none" stroke="none"/>
          </defs>
          
          <g id="Group 1">
            <line id="Line 2" stroke="var(--stroke-0, #5D5959)" x1="3.92771" x2="439.146" y1="4.24722" y2="4.24722" />
            <path d={svgPaths.p50280} id="Line 7" stroke="var(--stroke-0, #5D5959)" />
            <line id="Line 3" stroke="var(--stroke-0, #5D5959)" x1="3.92771" x2="439.146" y1="86.1975" y2="86.1975" />
            <line id="Line 8" stroke="var(--stroke-0, #5D5959)" x1="254.196" x2="254.196" y1="440.345" y2="5.12674" />
            <line id="Line 4" stroke="var(--stroke-0, #5D5959)" x1="3.92771" x2="439.146" y1="168.148" y2="168.148" />
            <line id="Line 9" stroke="var(--stroke-0, #5D5959)" x1="170.606" x2="170.606" y1="440.725" y2="5.50638" />
            <line id="Line 5" stroke="var(--stroke-0, #5D5959)" x1="3.92771" x2="439.146" y1="250.098" y2="250.098" />
            <line id="Line 10" stroke="var(--stroke-0, #5D5959)" x1="87.017" x2="87.017" y1="441.104" y2="5.88596" />
            <line id="Line 6" stroke="var(--stroke-0, #5D5959)" x1="3.92771" x2="439.146" y1="332.048" y2="332.048" />
            <path d={svgPaths.p1e0edc00} id="Line 12" stroke="var(--stroke-0, #5D5959)" />
            <line id="Line 11" stroke="var(--stroke-0, #5D5959)" x1="4.73694" x2="4.73694" y1="439.903" y2="5.23697" />
            
            {/* Interactive rectangles */}
            <rect 
              fill={hoveredRect === "rect8" ? "#00ffff" : "var(--fill-0, #D9D9D9)"} 
              height="9.16465" 
              id="Rectangle 8" 
              width="9.16465"
              className="cursor-pointer transition-all duration-300"
              filter={hoveredRect === "rect8" ? "url(#hoverGlow)" : ""}
              onMouseEnter={() => setHoveredRect("rect8")}
              onMouseLeave={() => setHoveredRect(null)}
            />
            <rect 
              fill={hoveredRect === "rect9" ? "#00ffff" : "var(--fill-0, #D9D9D9)"} 
              height="9.16465" 
              id="Rectangle 9" 
              width="9.16465" 
              y="79.8634"
              className="cursor-pointer transition-all duration-300"
              filter={hoveredRect === "rect9" ? "url(#hoverGlow)" : ""}
              onMouseEnter={() => setHoveredRect("rect9")}
              onMouseLeave={() => setHoveredRect(null)}
            />
            <rect
              fill={hoveredRect === "rect10" ? "#00ffff" : "var(--fill-0, #D9D9D9)"}
              height="9.16465"
              id="Rectangle 10"
              width="9.16465"
              x="82.4818"
              y="81.1726"
              className="cursor-pointer transition-all duration-300"
              filter={hoveredRect === "rect10" ? "url(#hoverGlow)" : ""}
              onMouseEnter={() => setHoveredRect("rect10")}
              onMouseLeave={() => setHoveredRect(null)}
            />
            <rect
              fill={hoveredRect === "rect12" ? "#00ffff" : "var(--fill-0, #D9D9D9)"}
              height="9.16465"
              id="Rectangle 12"
              width="9.16465"
              x="332.546"
              y="327.309"
              className="cursor-pointer transition-all duration-300"
              filter={hoveredRect === "rect12" ? "url(#hoverGlow)" : ""}
              onMouseEnter={() => setHoveredRect("rect12")}
              onMouseLeave={() => setHoveredRect(null)}
            />
            <rect 
              fill={hoveredRect === "rect11" ? "#00ffff" : "var(--fill-0, #D9D9D9)"} 
              height="9.16465" 
              id="Rectangle 11" 
              width="9.16465" 
              x="82.4818"
              className="cursor-pointer transition-all duration-300"
              filter={hoveredRect === "rect11" ? "url(#hoverGlow)" : ""}
              onMouseEnter={() => setHoveredRect("rect11")}
              onMouseLeave={() => setHoveredRect(null)}
            />
            
            {/* Neon light beam animation */}
            <g>
              {/* Wide outer glow - cyan/blue */}
              <circle r="12" fill="#0080ff" filter="url(#strongGlow)" opacity="0.3">
                <animateMotion dur="15s" repeatCount="indefinite">
                  <mpath href="#circuitPath"/>
                </animateMotion>
              </circle>
              
              {/* Medium glow - electric blue */}
              <circle r="6" fill="#00bfff" filter="url(#neonGlow)" opacity="0.7">
                <animateMotion dur="15s" repeatCount="indefinite">
                  <mpath href="#circuitPath"/>
                </animateMotion>
              </circle>
              
              {/* Main beam - bright cyan */}
              <circle r="3" fill="#00ffff" filter="url(#neonGlow)">
                <animateMotion dur="15s" repeatCount="indefinite">
                  <mpath href="#circuitPath"/>
                </animateMotion>
              </circle>
              
              {/* Core bright point - white */}
              <circle r="1.5" fill="#ffffff">
                <animateMotion dur="15s" repeatCount="indefinite">
                  <mpath href="#circuitPath"/>
                </animateMotion>
              </circle>
              
              {/* Trail effects with delays */}
              <circle r="4" fill="#00bfff" opacity="0.5">
                <animateMotion dur="15s" repeatCount="indefinite" begin="0.3s">
                  <mpath href="#circuitPath"/>
                </animateMotion>
              </circle>
              
              <circle r="3" fill="#0080ff" opacity="0.3">
                <animateMotion dur="15s" repeatCount="indefinite" begin="0.6s">
                  <mpath href="#circuitPath"/>
                </animateMotion>
              </circle>
              
              <circle r="2" fill="#0060df" opacity="0.2">
                <animateMotion dur="15s" repeatCount="indefinite" begin="0.9s">
                  <mpath href="#circuitPath"/>
                </animateMotion>
              </circle>
            </g>
          </g>
        </svg>
      </div>
    </div>
  );
}

function Header() {
  const [isStarHovered, setIsStarHovered] = useState(false);

  return (
    <header className="relative z-10 animate-fade-in-down">
      {/* Main header background */}
      <div className="backdrop-blur-[42.3px] backdrop-filter bg-[rgba(55,55,55,0.62)] h-[70px] sm:h-[80px] lg:h-[91px] mx-4 sm:mx-8 lg:mx-[98px] rounded-[25px] mt-[20px] sm:mt-[30px] lg:mt-[46px] hover:bg-[rgba(55,55,55,0.75)] transition-all duration-300">
        <div className="flex items-center justify-between h-full px-4 sm:px-6 lg:px-8">
          {/* Left side - Logo and tagline */}
          <div className="flex items-center gap-4 sm:gap-6 lg:gap-8">
            {/* Logo */}
            <div className="flex items-center gap-3">
              <div 
                className="flex h-[40px] sm:h-[50px] lg:h-[60px] w-[40px] sm:w-[50px] lg:w-[60px] items-center justify-center cursor-pointer"
                onMouseEnter={() => setIsStarHovered(true)}
                onMouseLeave={() => setIsStarHovered(false)}
              >
                <div className={`rotate-[29.505deg] transition-transform duration-500 ${isStarHovered ? 'animate-spin-slow scale-110' : ''}`}>
                  <div className="h-[30px] sm:h-[40px] lg:h-[50px] w-[30px] sm:w-[40px] lg:w-[50px] relative">
                    <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 53 50">
                      <path 
                        d={svgPaths.p19594300} 
                        fill={isStarHovered ? "#00ffff" : "var(--fill-0, #D9D9D9)"} 
                        id="Star 1" 
                        className="transition-colors duration-300"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <h1 className="font-['Splash:Regular',_sans-serif] text-[20px] sm:text-[25px] lg:text-[30px] text-[#ffffff] hover:text-[#00ffff] transition-colors duration-300 cursor-pointer">
                Motion
              </h1>
            </div>
            
            {/* Divider line - hidden on mobile */}
            <div className="hidden sm:block w-[1px] h-[40px] lg:h-[65px] bg-white opacity-60"></div>
            
            {/* Tagline - hidden on mobile */}
            <div className="hidden sm:block font-['Space_Mono:Regular',_sans-serif] text-[8px] lg:text-[10px] text-[#ffffff] max-w-[300px] opacity-70 hover:opacity-100 transition-opacity duration-300">
              no limit you're creativity ok just keep
            </div>
          </div>

          {/* Right side - Auth buttons */}
          <div className="flex items-center gap-3 sm:gap-4">
            <button className="font-['Poppins:Medium',_sans-serif] text-[15px] text-[#ffffff] hover:opacity-80 hover:scale-105 transition-all duration-200 px-3 py-2 rounded-md hover:bg-[rgba(255,255,255,0.1)]">
              Log in
            </button>
            <button className="backdrop-blur-lg backdrop-filter bg-[rgba(217,217,217,0.4)] hover:bg-[rgba(217,217,217,0.6)] hover:scale-105 transition-all duration-200 h-[35px] sm:h-[38px] lg:h-[41px] px-4 sm:px-5 lg:px-6 rounded-[10px] font-['Poppins:Medium',_sans-serif] text-[15px] text-[#000000] hover:shadow-lg hover:shadow-[#00ffff]/20">
              Sign up
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}

export default function Hero() {
  return (
    <div className="bg-[#040404] min-h-screen relative overflow-hidden" data-name="Desktop - 1">
      {/* Background */}
      <div className="absolute inset-0 bg-[#131313]" />
      
      {/* Header */}
      <Header />
      
      {/* Main content area */}
      <div className="relative z-10 max-w-[1440px] mx-auto px-4 sm:px-8 lg:px-[109px]">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center min-h-[calc(100vh-140px)]">
          {/* Left column - Hero text */}
          <div className="order-2 lg:order-1">
            <HeroText />
          </div>
          
          {/* Right column - Grid visualization */}
          <div className="order-1 lg:order-2 flex justify-center lg:justify-end items-center py-8 lg:py-0">
            <Group1 />
          </div>
        </div>
      </div>
    </div>
  );
}