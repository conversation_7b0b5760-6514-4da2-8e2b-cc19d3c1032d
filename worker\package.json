{"name": "worker", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsup src/index.ts --format cjs", "start": "node ./dist/index.js", "restart": "pnpm clean && pnpm build && pnpm start", "clean": "rm -rf ./dist"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.11.0", "dependencies": {"@aws-sdk/client-s3": "^3.837.0", "cors": "^2.8.5", "dotenv": "^16.6.0", "express": "^5.1.0", "fs-extra": "^11.3.0", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/fs-extra": "^11.0.4", "@types/node": "^24.0.4", "ts-node-dev": "^2.0.0", "tsup": "^8.5.0", "typescript": "^5.8.3"}}