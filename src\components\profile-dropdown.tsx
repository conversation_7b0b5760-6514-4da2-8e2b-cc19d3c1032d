"use client";

import { useState } from "react";
import { useSession, signOut } from "next-auth/react";
import { useTheme } from "next-themes";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Palette, FileText, LogOut, Sun, Moon, Monitor, Check, Crown, Github, ExternalLink } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { PricingPopup } from "./pricing-popup";

export function ProfileDropdown() {
  const session = useSession();
  const user = session.data?.user;
  const { setTheme, theme } = useTheme();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [pricingOpen, setPricingOpen] = useState(false);

  // Closing dropdown first to make sure popup is opened
  const handlePricingClick = () => {
    setDropdownOpen(false);
    setPricingOpen(true);
  };

  return (
    <>
      <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Avatar className="cursor-pointer">
            <AvatarImage
              src={user?.image}
              alt="user-profile"
              className="object-cover transition-all cursor-pointer"
            />
            <AvatarFallback className="cursor-pointer bg-teal-600">
              {user?.name?.[0].toUpperCase() || "U"}
            </AvatarFallback>
          </Avatar>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">
                {user?.name || "User"}
              </p>
              <p className="text-xs leading-none text-muted-foreground">
                {user?.email}
              </p>
            </div>
          </DropdownMenuLabel>

          <DropdownMenuSeparator />

          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <Palette className="mr-2 h-4 w-4" />
              <span>Appearance</span>
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              <DropdownMenuItem onClick={() => setTheme("light")}>
                <Sun className="mr-2 h-4 w-4" />
                <span>Light</span>
                {theme === "light" && <Check className="ml-auto h-4 w-4" />}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme("dark")}>
                <Moon className="mr-2 h-4 w-4" />
                <span>Dark</span>
                {theme === "dark" && <Check className="ml-auto h-4 w-4" />}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme("system")}>
                <Monitor className="mr-2 h-4 w-4" />
                <span>System</span>
                {theme === "system" && <Check className="ml-auto h-4 w-4" />}
              </DropdownMenuItem>
            </DropdownMenuSubContent>

            <DropdownMenuItem asChild>
              <Link href="https://github.com/manu0990/motion" target="_blank" rel="noopener noreferrer" className="cursor-pointer">
                <Github className="mr-2 h-4 w-4" />
                <span>GitHub</span>
                <ExternalLink className="ml-auto h-4 w-4" />
              </Link>
            </DropdownMenuItem>
          </DropdownMenuSub>

          <DropdownMenuSeparator />

          <DropdownMenuItem className="cursor-pointer" onClick={handlePricingClick} tabIndex={0}>
            <Crown className="mr-2 h-4 w-4" />
            <span>Upgrade plan</span>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href="/policies/" className="cursor-pointer">
              <FileText className="mr-2 h-4 w-4" />
              <span>Privacy Policy</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem
            className="cursor-pointer text-red-600 focus:text-red-600"
            onClick={() => signOut({ callbackUrl: "/" })}
          >
            <LogOut className="mr-2 h-4 w-4" />
            <span>Sign out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <PricingPopup open={pricingOpen} onOpenChange={setPricingOpen} />
    </>
  );
}
