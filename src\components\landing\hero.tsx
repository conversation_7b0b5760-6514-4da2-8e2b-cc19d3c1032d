'use client';

import { useState, useRef } from "react";
import { Lines } from "@/components/lines";

function HeroText() {
  const [isLoading, setIsLoading] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleMagneticEffect = (e: React.MouseEvent<HTMLButtonElement>) => {
    const button = buttonRef.current;
    if (!button) return;

    const rect = button.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    const moveX = (x - centerX) * 0.15;
    const moveY = (y - centerY) * 0.15;

    button.style.transform = `translate(${moveX}px, ${moveY}px) scale(1.02)`;
  };

  const handleMouseLeave = () => {
    const button = buttonRef.current;
    if (!button) return;
    button.style.transform = 'translate(0px, 0px) scale(1)';
  };

  const handleClick = () => {
    setIsLoading(true);
    // Create ripple effect
    const button = buttonRef.current;
    if (button) {
      const ripple = document.createElement('span');
      ripple.className = 'absolute inset-0 rounded-[65px] bg-white opacity-20 animate-ping';
      button.appendChild(ripple);
      setTimeout(() => ripple.remove(), 600);
    }
    // Simulate loading
    setTimeout(() => setIsLoading(false), 2000);
  };

  return (
    <div className="flex flex-col justify-center min-h-[500px] lg:min-h-[600px] px-4 lg:px-0 animate-fade-in-up">
      <div className="max-w-[627px]">
        <h1 className="font-['Poppins:SemiBold',_sans-serif] text-[28px] sm:text-[35px] lg:text-[40px] text-[#ffffff] leading-[1.2] mb-8 lg:mb-12 animate-fade-in-up animation-delay-200">
          Transforming you're equation a visual edge
        </h1>
        <p className="font-['Poppins:Regular',_sans-serif] text-[16px] sm:text-[18px] lg:text-[20px] text-[#ffffff] leading-[1.4] mb-8 lg:mb-12 max-w-[425px] animate-fade-in-up animation-delay-400">
          Make every equation to a life that visualize better using generative AI
        </p>
        <button
          ref={buttonRef}
          onMouseMove={handleMagneticEffect}
          onMouseLeave={handleMouseLeave}
          onClick={handleClick}
          disabled={isLoading}
          className="relative bg-[#343434] hover:bg-[#4a4a4a] transition-all duration-200 h-[42px] px-6 rounded-[65px] font-['Inter:Semi_Bold',_sans-serif] font-semibold text-[15px] text-[#ffffff] hover:shadow-lg hover:shadow-[#FFFFFF]/20 animate-fade-in-up animation-delay-600 disabled:opacity-70 overflow-hidden"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Loading...
            </div>
          ) : (
            'Get started'
          )}
        </button>
      </div>
    </div>
  );
}

export function Hero() {
  return (
    <div className="bg-[#040404] min-h-screen relative overflow-hidden" data-name="Desktop - 1">
      {/* Background */}
      <div className="absolute inset-0 bg-[#131313]" />

      {/* Main content area */}
      <div className="relative z-10 max-w-[1440px] mx-auto px-4 sm:px-8 lg:px-[109px]">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center min-h-[calc(100vh-140px)]">
          {/* Left column - Hero text */}
          <div className="order-2 lg:order-1">
            <HeroText />
          </div>

          {/* Right column - Grid visualization */}
          <div className="order-1 lg:order-2 flex justify-center lg:justify-end items-center py-8 lg:py-0">
            <Lines />
          </div>
        </div>
      </div>
    </div>
  );
}