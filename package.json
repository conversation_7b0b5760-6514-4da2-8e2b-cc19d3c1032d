{"name": "motion", "version": "0.1.0", "private": true, "scripts": {"dev": "prisma generate && next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "db:gen": "prisma generate", "db:migrate": "prisma migrate dev --name", "db:migrate:prod": "prisma migrate deploy", "db:studio": "prisma studio", "restart": "rm -rf .next && rm -rf node_modules && pnpm i && pnpm run db:gen && pnpm run dev"}, "dependencies": {"@aws-sdk/client-s3": "^3.848.0", "@hookform/resolvers": "^5.1.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "6.8.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next": "15.1.8", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "openai": "^5.10.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "sonner": "^2.0.6", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/node": "^20.19.9", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9.31.0", "eslint-config-next": "15.1.8", "postcss": "^8.5.6", "prisma": "^6.12.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977", "engines": {"node": ">=20.0.0"}}