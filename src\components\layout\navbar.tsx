"use client";

import { useState } from "react";
import svgPaths from "@/components/imports/svg-9r6m2ztw2h";
import Link from "next/link";

export function Navbar() {
  const [isStarHovered, setIsStarHovered] = useState(false);

  return (
    <header className="relative z-10 animate-fade-in-down">
      {/* Main header background */}
      <div className="backdrop-blur-[42.3px] backdrop-filter bg-[rgba(55,55,55,0.62)] h-[70px] sm:h-[80px] lg:h-[91px] mx-4 sm:mx-8 lg:mx-[98px] rounded-[25px] mt-[20px] sm:mt-[30px] lg:mt-[46px] hover:bg-[rgba(55,55,55,0.75)] transition-all duration-300">
        <div className="flex items-center justify-between h-full px-4 sm:px-6 lg:px-8">
          {/* Left side - Logo and tagline */}
          <div className="flex items-center gap-4 sm:gap-6 lg:gap-8">
            {/* Logo */}
            <div className="flex items-center gap-3">
              <div
                className="flex h-[40px] sm:h-[50px] lg:h-[60px] w-[40px] sm:w-[50px] lg:w-[60px] items-center justify-center cursor-pointer"
                onMouseEnter={() => setIsStarHovered(true)}
                onMouseLeave={() => setIsStarHovered(false)}
              >
                <div className={`rotate-[29.505deg] transition-transform duration-500 ${isStarHovered ? 'animate-spin-slow scale-110' : ''}`}>
                  <div className="h-[30px] sm:h-[40px] lg:h-[50px] w-[30px] sm:w-[40px] lg:w-[50px] relative">
                    <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 53 50">
                      <path
                        d={svgPaths.p19594300}
                        fill={isStarHovered ? "#00ffff" : "var(--fill-0, #D9D9D9)"}
                        id="Star 1"
                        className="transition-colors duration-300"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <h1 className="font-['Splash:Regular',_sans-serif] text-[20px] sm:text-[25px] lg:text-[30px] text-[#ffffff] hover:text-[#00ffff] transition-colors duration-300 cursor-pointer">
                Motion
              </h1>
            </div>

            {/* Divider line - hidden on mobile */}
            <div className="hidden sm:block w-[1px] h-[40px] lg:h-[65px] bg-white opacity-60"></div>

            {/* Tagline - hidden on mobile */}
            <div className="hidden sm:block font-['Space_Mono:Regular',_sans-serif] text-[8px] lg:text-[10px] text-[#ffffff] max-w-[300px] opacity-70 hover:opacity-100 transition-opacity duration-300">
              no limit you're creativity ok just keep
            </div>
          </div>

          {/* Right side - Auth buttons */}
          <div className="flex items-center gap-3 sm:gap-4">
            <Link href="/auth/login">
              <button className="font-['Poppins:Medium',_sans-serif] text-[15px] text-[#ffffff] hover:opacity-80 hover:scale-105 transition-all duration-200 px-3 py-2 rounded-md hover:bg-[rgba(255,255,255,0.1)]">
                Log in
              </button>
            </Link>
            <Link href="/auth/signup">
              <button className="backdrop-blur-lg backdrop-filter bg-[rgba(217,217,217,0.4)] hover:bg-[rgba(217,217,217,0.6)] hover:scale-105 transition-all duration-200 h-[35px] sm:h-[38px] lg:h-[41px] px-4 sm:px-5 lg:px-6 rounded-[10px] font-['Poppins:Medium',_sans-serif] text-[15px] text-[#000000] hover:shadow-lg hover:shadow-[#FFFFFF]/20">
                Sign up
              </button>
            </Link>
          </div>
        </div>
      </div>
    </header >
  );
}